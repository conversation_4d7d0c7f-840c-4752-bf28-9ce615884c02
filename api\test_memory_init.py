#!/usr/bin/env python3
"""
Test script to initialize memory client and check status afterwards.
"""

import sys
import os
import json
from datetime import datetime

# Add the current directory to Python path so we can import app modules
sys.path.insert(0, '/mnt/d/_<PERSON>-Dev/memory-master/api')

def test_memory_initialization():
    """Test memory client initialization and monitor status changes."""
    try:
        from app.utils.memory import get_memory_client, MemoryClientSingleton
        
        print("=== Memory Client Initialization Test ===")
        print(f"Timestamp: {datetime.now().isoformat()}")
        print()
        
        # Get current status before initialization
        memory_singleton = MemoryClientSingleton()
        print("BEFORE INITIALIZATION:")
        print(f"  Client exists: {memory_singleton._client is not None}")
        print(f"  Is healthy: {memory_singleton.is_healthy()}")
        print(f"  Degraded mode: {memory_singleton._degraded_mode}")
        print()
        
        # Attempt to initialize memory client
        print("INITIALIZING MEMORY CLIENT...")
        print("This may take a few seconds...")
        
        client = get_memory_client()
        
        print()
        print("AFTER INITIALIZATION:")
        print(f"  Client returned: {client is not None}")
        print(f"  Client exists: {memory_singleton._client is not None}")
        print(f"  Is healthy: {memory_singleton.is_healthy()}")
        print(f"  Degraded mode: {memory_singleton._degraded_mode}")
        
        if memory_singleton._degraded_mode:
            print(f"  Degradation reason: {memory_singleton._degradation_reason}")
        
        # Get updated queue status
        print()
        print("UPDATED QUEUE STATUS:")
        queue_status = memory_singleton.get_operation_queue_status()
        for key, value in queue_status.items():
            print(f"  {key}: {value}")
        
        # Get connectivity status
        print()
        print("CONNECTIVITY STATUS:")
        connectivity = memory_singleton.get_connectivity_status()
        for key, value in connectivity.items():
            if key in ['last_check_time', 'last_alert_time', 'next_check_time']:
                if value:
                    formatted_time = datetime.fromtimestamp(value).isoformat()
                    print(f"  {key}: {formatted_time}")
                else:
                    print(f"  {key}: None")
            elif key == 'uptime_percentage':
                if value is not None:
                    print(f"  {key}: {value:.2%}")
                else:
                    print(f"  {key}: None")
            else:
                print(f"  {key}: {value}")
        
        # Test a simple operation if client exists
        if client:
            print()
            print("TESTING MEMORY OPERATION:")
            try:
                # Test a simple search to verify functionality
                result = client.search("test query", limit=1)
                print(f"  Search test: SUCCESS (returned {len(result.get('results', []))} results)")
            except Exception as e:
                print(f"  Search test: FAILED - {e}")
                # Check if this triggered degraded mode
                if memory_singleton._degraded_mode:
                    print(f"  Entered degraded mode: {memory_singleton._degradation_reason}")
        
        return client
        
    except Exception as e:
        print(f"Error during initialization test: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_memory_initialization()