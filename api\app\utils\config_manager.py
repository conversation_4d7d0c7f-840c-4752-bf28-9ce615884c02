"""
Configuration Hot-Reload Manager

This module provides a thread-safe singleton configuration manager that supports
hot-reloading of non-critical configuration changes without requiring application restart.
"""

import threading
import time
import logging
import hashlib
import json
import os
import socket
from typing import Dict, Any, List, Callable, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from app.database import SessionLocal
from app.models import Config as ConfigModel


class ConfigChangeType(Enum):
    """Types of configuration changes."""
    NON_CRITICAL = "non_critical"  # Can be hot-reloaded
    CRITICAL = "critical"          # Requires restart
    VALIDATION_ERROR = "validation_error"  # Invalid configuration


@dataclass
class ConfigValidationResult:
    """Result of configuration validation."""
    valid: bool
    errors: List[str]
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []


@dataclass
class ConfigChangeEvent:
    """Configuration change event data."""
    old_config: Dict[str, Any]
    new_config: Dict[str, Any]
    change_type: ConfigChangeType
    requires_restart: bool
    config_version: int
    timestamp: float


# Type alias for configuration change listeners
ConfigChangeListener = Callable[[ConfigChangeEvent], None]


class ConfigManager:
    """
    Thread-safe singleton configuration manager with hot-reload support.
    
    Features:
    - Hot-reload for non-critical configuration changes
    - Configuration validation with detailed error reporting
    - Configuration versioning and change tracking
    - Observer pattern for configuration change notifications
    - Thread-safe operations with proper locking
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __init__(self):
        """Initialize the configuration manager."""
        if ConfigManager._instance is not None:
            raise RuntimeError("ConfigManager is a singleton. Use get_instance() instead.")
        
        self._config: Dict[str, Any] = {}
        self._config_version: int = 0
        self._config_lock = threading.RLock()  # Reentrant lock for nested operations
        self._change_listeners: List[ConfigChangeListener] = []
        self._listeners_lock = threading.Lock()
        self._last_db_check: float = 0
        self._db_check_interval: float = 5.0  # Check database every 5 seconds
        self._config_hash: Optional[str] = None
        
        # Critical configuration fields that require restart
        self._critical_fields = {
            'mem0.llm.provider',
            'mem0.embedder.provider', 
            'mem0.llm.config.api_key',
            'mem0.embedder.config.api_key',
            'vector_store.provider',
            'vector_store.config',
            'vector_store.config.host',
            'vector_store.config.port',
            'qdrant.host',
            'qdrant.port'
        }
        
        # Load initial configuration
        self._load_config()
        
        logging.info("ConfigManager initialized successfully")
    
    @classmethod
    def get_instance(cls) -> 'ConfigManager':
        """Get the singleton instance of ConfigManager."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance
    
    def _load_config(self) -> None:
        """Load configuration from database."""
        try:
            db = SessionLocal()
            try:
                db_config = db.query(ConfigModel).filter(ConfigModel.key == "main").first()
                if db_config and db_config.value:
                    with self._config_lock:
                        self._config = db_config.value.copy()
                        self._config_version += 1
                        self._config_hash = self._calculate_config_hash(self._config)
                        self._last_db_check = time.time()
                        
                    logging.info(f"Configuration loaded from database (version {self._config_version})")
                else:
                    logging.info("No configuration found in database, initializing with default configuration")
                    with self._config_lock:
                        self._config = self.get_default_config()
                        self._config_version += 1
                        self._config_hash = self._calculate_config_hash(self._config)
                        self._last_db_check = time.time()
                    
                    # Save default configuration to database
                    try:
                        db_config = ConfigModel(key="main", value=self._config)
                        db.add(db_config)
                        db.commit()
                        logging.info("Default configuration saved to database")
                    except Exception as save_error:
                        logging.error(f"Failed to save default configuration to database: {save_error}")
                        db.rollback()
                    
            finally:
                db.close()
                
        except Exception as e:
            logging.error(f"Failed to load configuration from database: {e}")
            # Continue with empty config rather than failing
    
    def _calculate_config_hash(self, config: Dict[str, Any]) -> str:
        """Calculate hash of configuration for change detection."""
        try:
            config_str = json.dumps(config, sort_keys=True, default=str)
            return hashlib.sha256(config_str.encode()).hexdigest()
        except Exception as e:
            logging.warning(f"Failed to calculate config hash: {e}")
            return str(time.time())  # Fallback to timestamp
    
    def get_config(self) -> Dict[str, Any]:
        """Get current configuration (thread-safe copy)."""
        with self._config_lock:
            return self._config.copy()
    
    def get_config_value(self, key_path: str, default: Any = None) -> Any:
        """
        Get a specific configuration value using dot notation.
        
        Args:
            key_path: Dot-separated path to the config value (e.g., 'mem0.llm.config.model')
            default: Default value if key is not found
            
        Returns:
            Configuration value or default
        """
        with self._config_lock:
            try:
                value = self._config
                for key in key_path.split('.'):
                    value = value[key]
                return value
            except (KeyError, TypeError):
                return default
    
    def update_config(self, new_config: Dict[str, Any], save_to_db: bool = True) -> Dict[str, Any]:
        """
        Update configuration with validation and hot-reload support.
        
        Args:
            new_config: New configuration values to apply
            save_to_db: Whether to save changes to database
            
        Returns:
            Dictionary with update result information
            
        Raises:
            ValueError: If configuration validation fails
        """
        with self._config_lock:
            # Validate configuration changes
            validation_result = self._validate_config(new_config)
            if not validation_result.valid:
                raise ValueError(f"Invalid configuration: {', '.join(validation_result.errors)}")
            
            # Store old config for change event
            old_config = self._config.copy()
            
            # Determine change type and restart requirement
            change_type, requires_restart = self._analyze_config_changes(old_config, new_config)
            
            # Apply configuration changes
            self._config.update(new_config)
            self._config_version += 1
            new_hash = self._calculate_config_hash(self._config)
            
            # Save to database if requested
            if save_to_db:
                self._save_config_to_db()
            
            # Create change event
            change_event = ConfigChangeEvent(
                old_config=old_config,
                new_config=self._config.copy(),
                change_type=change_type,
                requires_restart=requires_restart,
                config_version=self._config_version,
                timestamp=time.time()
            )
            
            # Notify listeners
            self._notify_listeners(change_event)
            
            # Update hash after successful change
            self._config_hash = new_hash
            
            logging.info(f"Configuration updated to version {self._config_version} "
                        f"(type: {change_type.value}, restart_required: {requires_restart})")
            
            return {
                "success": True,
                "requires_restart": requires_restart,
                "config_version": self._config_version,
                "change_type": change_type.value,
                "warnings": validation_result.warnings
            }

    def _validate_config(self, config: Dict[str, Any]) -> ConfigValidationResult:
        """
        Validate configuration changes.

        Args:
            config: Configuration to validate

        Returns:
            ConfigValidationResult with validation details
        """
        errors = []
        warnings = []

        # Validate mem0 configuration if present
        if 'mem0' in config:
            mem0_config = config['mem0']

            # Validate LLM configuration
            if 'llm' in mem0_config:
                llm_config = mem0_config['llm']
                if 'provider' in llm_config and not llm_config['provider']:
                    errors.append("mem0.llm.provider cannot be empty")

                if 'config' in llm_config:
                    llm_inner_config = llm_config['config']

                    # Validate API key
                    if 'api_key' in llm_inner_config:
                        api_key = llm_inner_config['api_key']
                        if not api_key or (isinstance(api_key, str) and not api_key.strip()):
                            errors.append("mem0.llm.config.api_key cannot be empty")
                        elif isinstance(api_key, str) and api_key.startswith('env:'):
                            # Validate environment variable exists
                            env_var = api_key[4:]  # Remove 'env:' prefix
                            import os
                            if not os.getenv(env_var):
                                warnings.append(f"Environment variable {env_var} is not set")

                    # Validate model parameters
                    if 'temperature' in llm_inner_config:
                        temp = llm_inner_config['temperature']
                        if not isinstance(temp, (int, float)) or not (0 <= temp <= 1):
                            errors.append("mem0.llm.config.temperature must be a number between 0 and 1")

                    if 'max_tokens' in llm_inner_config:
                        max_tokens = llm_inner_config['max_tokens']
                        if not isinstance(max_tokens, int) or max_tokens <= 0:
                            errors.append("mem0.llm.config.max_tokens must be a positive integer")

            # Validate embedder configuration
            if 'embedder' in mem0_config:
                embedder_config = mem0_config['embedder']
                if 'provider' in embedder_config and not embedder_config['provider']:
                    errors.append("mem0.embedder.provider cannot be empty")

                if 'config' in embedder_config:
                    embedder_inner_config = embedder_config['config']

                    # Validate API key
                    if 'api_key' in embedder_inner_config:
                        api_key = embedder_inner_config['api_key']
                        if not api_key or (isinstance(api_key, str) and not api_key.strip()):
                            errors.append("mem0.embedder.config.api_key cannot be empty")

        # Validate openmemory configuration if present
        if 'openmemory' in config:
            openmemory_config = config['openmemory']

            if 'max_text_length' in openmemory_config:
                max_length = openmemory_config['max_text_length']
                if not isinstance(max_length, int) or max_length <= 0:
                    errors.append("openmemory.max_text_length must be a positive integer")
                elif max_length > 10000:
                    warnings.append("openmemory.max_text_length is very large, may impact performance")

        # Validate vector store configuration if present
        if 'vector_store' in config:
            vector_store_config = config['vector_store']
            
            # Validate provider
            if 'provider' in vector_store_config:
                provider = vector_store_config['provider']
                if not provider:
                    errors.append("vector_store.provider cannot be empty")
                elif provider not in ['qdrant', 'chroma', 'pinecone', 'weaviate']:
                    warnings.append(f"vector_store.provider '{provider}' may not be supported")
            
            # Validate configuration for specific providers
            if 'config' in vector_store_config:
                vs_config = vector_store_config['config']
                
                # Validate Qdrant-specific configuration
                if vector_store_config.get('provider') == 'qdrant':
                    self._validate_qdrant_config(vs_config, errors, warnings)

        # Validate root-level Qdrant configuration if present
        if 'qdrant' in config:
            qdrant_config = config['qdrant']
            self._validate_qdrant_config(qdrant_config, errors, warnings)

        # Validate user-specific settings
        if 'user' in config:
            user_config = config['user']
            
            if 'collection_name' in user_config:
                collection_name = user_config['collection_name']
                if not isinstance(collection_name, str) or not collection_name.strip():
                    errors.append("user.collection_name must be a non-empty string")
                elif len(collection_name) > 100:
                    warnings.append("user.collection_name is very long, may cause issues")

        return ConfigValidationResult(
            valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )

    def _validate_qdrant_config(self, qdrant_config: Dict[str, Any], errors: List[str], warnings: List[str]) -> None:
        """
        Validate Qdrant-specific configuration parameters.
        
        Args:
            qdrant_config: Qdrant configuration dictionary
            errors: List to append validation errors
            warnings: List to append validation warnings
        """
        # Validate host
        if 'host' in qdrant_config:
            host = qdrant_config['host']
            if not isinstance(host, str) or not host.strip():
                errors.append("qdrant.host must be a non-empty string")
            else:
                # Validate host format
                host = host.strip()
                if host in ['localhost', '127.0.0.1']:
                    warnings.append("qdrant.host is localhost, ensure Qdrant is running locally")
                elif not self._is_valid_hostname_or_ip(host):
                    errors.append(f"qdrant.host '{host}' is not a valid hostname or IP address")
                else:
                    # Test remote Qdrant connectivity
                    self._test_qdrant_connectivity(host, qdrant_config.get('port', 6333), warnings)
        
        # Validate port
        if 'port' in qdrant_config:
            port = qdrant_config['port']
            if not isinstance(port, int):
                try:
                    port = int(port)
                    qdrant_config['port'] = port  # Update the config with the converted value
                except (ValueError, TypeError):
                    errors.append("qdrant.port must be a valid integer")
                    return
            
            if not (1 <= port <= 65535):
                errors.append("qdrant.port must be between 1 and 65535")
        
        # Validate collection name
        if 'collection_name' in qdrant_config:
            collection_name = qdrant_config['collection_name']
            if not isinstance(collection_name, str) or not collection_name.strip():
                errors.append("qdrant.collection_name must be a non-empty string")
            elif len(collection_name) > 100:
                warnings.append("qdrant.collection_name is very long, may cause issues")
            elif not collection_name.replace('_', '').replace('-', '').isalnum():
                warnings.append("qdrant.collection_name contains special characters, may cause issues")
        
        # Validate API key if present
        if 'api_key' in qdrant_config:
            api_key = qdrant_config['api_key']
            if api_key and not isinstance(api_key, str):
                errors.append("qdrant.api_key must be a string")
            elif isinstance(api_key, str) and api_key.startswith('env:'):
                # Validate environment variable exists
                env_var = api_key[4:]  # Remove 'env:' prefix
                if not os.getenv(env_var):
                    warnings.append(f"Environment variable {env_var} for Qdrant API key is not set")
        
        # Validate URL if present
        if 'url' in qdrant_config:
            url = qdrant_config['url']
            if not isinstance(url, str) or not url.strip():
                errors.append("qdrant.url must be a non-empty string")
            else:
                # Basic URL validation
                if not url.startswith(('http://', 'https://')):
                    errors.append("qdrant.url must start with http:// or https://")

    def _is_valid_hostname_or_ip(self, hostname: str) -> bool:
        """
        Validate if a string is a valid hostname or IP address.
        
        Args:
            hostname: String to validate
            
        Returns:
            bool: True if valid hostname or IP, False otherwise
        """
        try:
            # Try to parse as IP address first
            socket.inet_aton(hostname)
            return True
        except socket.error:
            # Not an IP address, check if it's a valid hostname
            try:
                # Basic hostname validation
                if not hostname or len(hostname) > 253:
                    return False
                
                # Check each label in the hostname
                labels = hostname.split('.')
                for label in labels:
                    if not label or len(label) > 63:
                        return False
                    if not label.replace('-', '').replace('_', '').isalnum():
                        return False
                    if label.startswith('-') or label.endswith('-'):
                        return False
                
                return True
            except Exception:
                return False

    def _test_qdrant_connectivity(self, host: str, port: int, warnings: List[str]) -> None:
        """
        Test connectivity to remote Qdrant instance.
        
        Args:
            host: Qdrant host
            port: Qdrant port
            warnings: List to append connectivity warnings
        """
        try:
            # Quick TCP connectivity test
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)  # 5 second timeout
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result != 0:
                warnings.append(f"Cannot connect to Qdrant at {host}:{port} - service may be down")
            else:
                logging.info(f"Successfully validated connectivity to Qdrant at {host}:{port}")
        except Exception as e:
            warnings.append(f"Failed to test Qdrant connectivity at {host}:{port}: {str(e)}")

    def get_default_config(self) -> Dict[str, Any]:
        """
        Get default configuration with remote Qdrant settings.
        
        Returns:
            Dict[str, Any]: Default configuration dictionary
        """
        # Get remote Qdrant settings from environment or use defaults
        qdrant_host = os.environ.get('QDRANT_HOST', 'localhost')
        qdrant_port = int(os.environ.get('QDRANT_PORT', '6333'))
        user_id = os.environ.get('USER', 'default_user')
        
        return {
            "mem0": {
                "llm": {
                    "provider": "openai",
                    "config": {
                        "model": "gpt-4o-mini",
                        "temperature": 0.2,
                        "max_tokens": 2000,
                        "api_key": "env:OPENAI_API_KEY"
                    }
                },
                "embedder": {
                    "provider": "openai",
                    "config": {
                        "model": "text-embedding-3-small",
                        "api_key": "env:OPENAI_API_KEY"
                    }
                },
                "vector_store": {
                    "provider": "qdrant",
                    "config": {
                        "collection_name": f"{user_id}_memories",
                        "url": f"http://{qdrant_host}:{qdrant_port}"
                    }
                },
                "version": "v1.1"
            },
            "openmemory": {
                "max_text_length": 5000,
                "custom_instructions": ""
            },
            "qdrant": {
                "host": qdrant_host,
                "port": qdrant_port,
                "collection_name": f"{user_id}_memories"
            },
            "user": {
                "id": user_id,
                "collection_name": f"{user_id}_memories"
            }
        }

    def _analyze_config_changes(self, old_config: Dict[str, Any], new_config: Dict[str, Any]) -> Tuple[ConfigChangeType, bool]:
        """
        Analyze configuration changes to determine if restart is required.

        Args:
            old_config: Previous configuration
            new_config: New configuration values

        Returns:
            Tuple of (change_type, requires_restart)
        """
        requires_restart = False

        # Check if any critical fields are being changed
        for field_path in self._critical_fields:
            old_value = self._get_nested_value(old_config, field_path)
            new_value = self._get_nested_value(new_config, field_path)

            if new_value is not None and old_value != new_value:
                requires_restart = True
                logging.info(f"Critical configuration change detected: {field_path}")
                break

        change_type = ConfigChangeType.CRITICAL if requires_restart else ConfigChangeType.NON_CRITICAL
        return change_type, requires_restart

    def _get_nested_value(self, config: Dict[str, Any], key_path: str) -> Any:
        """Get nested configuration value using dot notation."""
        try:
            value = config
            for key in key_path.split('.'):
                value = value[key]
            return value
        except (KeyError, TypeError):
            return None

    def _save_config_to_db(self) -> None:
        """Save current configuration to database."""
        try:
            db = SessionLocal()
            try:
                db_config = db.query(ConfigModel).filter(ConfigModel.key == "main").first()

                if db_config:
                    db_config.value = self._config
                    db_config.updated_at = None  # Trigger automatic timestamp update
                else:
                    db_config = ConfigModel(key="main", value=self._config)
                    db.add(db_config)

                db.commit()
                logging.debug("Configuration saved to database")

            finally:
                db.close()

        except Exception as e:
            logging.error(f"Failed to save configuration to database: {e}")
            raise

    def add_change_listener(self, listener: ConfigChangeListener) -> None:
        """
        Add a configuration change listener.

        Args:
            listener: Callable that will be notified of configuration changes
        """
        with self._listeners_lock:
            if listener not in self._change_listeners:
                self._change_listeners.append(listener)
                logging.debug(f"Added configuration change listener: {listener.__name__}")

    def remove_change_listener(self, listener: ConfigChangeListener) -> None:
        """
        Remove a configuration change listener.

        Args:
            listener: Listener to remove
        """
        with self._listeners_lock:
            if listener in self._change_listeners:
                self._change_listeners.remove(listener)
                logging.debug(f"Removed configuration change listener: {listener.__name__}")

    def _notify_listeners(self, change_event: ConfigChangeEvent) -> None:
        """
        Notify all registered listeners of configuration changes.

        Args:
            change_event: Configuration change event data
        """
        with self._listeners_lock:
            listeners = self._change_listeners.copy()  # Copy to avoid lock during iteration

        for listener in listeners:
            try:
                listener(change_event)
            except Exception as e:
                logging.error(f"Error in configuration change listener {listener.__name__}: {e}")

    def check_for_updates(self) -> bool:
        """
        Check for configuration updates from database.

        Returns:
            True if configuration was updated, False otherwise
        """
        current_time = time.time()
        if current_time - self._last_db_check < self._db_check_interval:
            return False

        try:
            db = SessionLocal()
            try:
                db_config = db.query(ConfigModel).filter(ConfigModel.key == "main").first()
                if not db_config or not db_config.value:
                    return False

                # Calculate hash of database config
                db_config_hash = self._calculate_config_hash(db_config.value)

                with self._config_lock:
                    if db_config_hash != self._config_hash:
                        # Configuration has changed in database
                        old_config = self._config.copy()
                        self._config = db_config.value.copy()
                        self._config_version += 1
                        self._config_hash = db_config_hash

                        # Analyze changes and notify listeners
                        change_type, requires_restart = self._analyze_config_changes(old_config, self._config)

                        change_event = ConfigChangeEvent(
                            old_config=old_config,
                            new_config=self._config.copy(),
                            change_type=change_type,
                            requires_restart=requires_restart,
                            config_version=self._config_version,
                            timestamp=current_time
                        )

                        self._notify_listeners(change_event)

                        logging.info(f"Configuration updated from database (version {self._config_version})")
                        return True

                return False

            finally:
                db.close()
                self._last_db_check = current_time

        except Exception as e:
            logging.error(f"Failed to check for configuration updates: {e}")
            return False

    def get_version(self) -> int:
        """Get current configuration version."""
        with self._config_lock:
            return self._config_version

    def reset(self) -> None:
        """Reset configuration manager (mainly for testing)."""
        with self._config_lock:
            self._config = {}
            self._config_version = 0
            self._config_hash = None
            self._last_db_check = 0

        with self._listeners_lock:
            self._change_listeners.clear()

        logging.info("ConfigManager reset")

    def validate_qdrant_connectivity(self) -> Dict[str, Any]:
        """
        Validate remote Qdrant connectivity using current configuration.
        
        Returns:
            Dict[str, Any]: Validation result with connectivity status
        """
        with self._config_lock:
            config = self._config.copy()
        
        result = {
            "valid": False,
            "host": None,
            "port": None,
            "collection_name": None,
            "errors": [],
            "warnings": []
        }
        
        # Check for Qdrant configuration
        qdrant_config = None
        if 'qdrant' in config:
            qdrant_config = config['qdrant']
        elif 'mem0' in config and 'vector_store' in config['mem0']:
            vs_config = config['mem0']['vector_store']
            if vs_config.get('provider') == 'qdrant' and 'config' in vs_config:
                qdrant_config = vs_config['config']
        elif 'vector_store' in config:
            vs_config = config['vector_store']
            if vs_config.get('provider') == 'qdrant' and 'config' in vs_config:
                qdrant_config = vs_config['config']
        
        if not qdrant_config:
            result["errors"].append("No Qdrant configuration found")
            return result
        
        # Extract connection details
        host = qdrant_config.get('host', os.environ.get('QDRANT_HOST', 'localhost'))
        port = qdrant_config.get('port', int(os.environ.get('QDRANT_PORT', '6333')))
        collection_name = qdrant_config.get('collection_name', f"{os.environ.get('USER', 'default_user')}_memories")
        
        result["host"] = host
        result["port"] = port
        result["collection_name"] = collection_name
        
        # Validate configuration
        temp_errors = []
        temp_warnings = []
        self._validate_qdrant_config(qdrant_config, temp_errors, temp_warnings)
        
        result["errors"].extend(temp_errors)
        result["warnings"].extend(temp_warnings)
        
        if temp_errors:
            return result
        
        # Test actual connectivity
        try:
            import requests
            import json
            
            # Test basic connectivity
            url = f"http://{host}:{port}/collections"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                collections_data = response.json()
                collections = collections_data.get('result', {}).get('collections', [])
                
                # Check if user's collection exists
                user_collection_exists = any(
                    col.get('name') == collection_name 
                    for col in collections
                )
                
                if user_collection_exists:
                    result["warnings"].append(f"Collection '{collection_name}' already exists")
                else:
                    result["warnings"].append(f"Collection '{collection_name}' will be created on first use")
                
                result["valid"] = True
                logging.info(f"Successfully validated Qdrant connectivity to {host}:{port}")
                
            else:
                result["errors"].append(f"Qdrant returned status code {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            result["errors"].append(f"Cannot connect to Qdrant at {host}:{port} - connection refused")
        except requests.exceptions.Timeout:
            result["errors"].append(f"Connection to Qdrant at {host}:{port} timed out")
        except requests.exceptions.RequestException as e:
            result["errors"].append(f"Request to Qdrant failed: {str(e)}")
        except ImportError:
            # Fallback to socket-based test if requests is not available
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(10)
                sock_result = sock.connect_ex((host, port))
                sock.close()
                
                if sock_result == 0:
                    result["valid"] = True
                    result["warnings"].append("Basic connectivity test passed (HTTP test unavailable)")
                else:
                    result["errors"].append(f"Cannot connect to Qdrant at {host}:{port}")
            except Exception as e:
                result["errors"].append(f"Connectivity test failed: {str(e)}")
        except Exception as e:
            result["errors"].append(f"Unexpected error during connectivity test: {str(e)}")
        
        return result

    def get_qdrant_config(self) -> Dict[str, Any]:
        """
        Get current Qdrant configuration.
        
        Returns:
            Dict[str, Any]: Current Qdrant configuration
        """
        with self._config_lock:
            config = self._config.copy()
        
        # Check for Qdrant configuration in different locations
        if 'qdrant' in config:
            return config['qdrant']
        elif 'mem0' in config and 'vector_store' in config['mem0']:
            vs_config = config['mem0']['vector_store']
            if vs_config.get('provider') == 'qdrant' and 'config' in vs_config:
                return vs_config['config']
        elif 'vector_store' in config:
            vs_config = config['vector_store']
            if vs_config.get('provider') == 'qdrant' and 'config' in vs_config:
                return vs_config['config']
        
        # Return default configuration if not found
        return {
            "host": os.environ.get('QDRANT_HOST', 'localhost'),
            "port": int(os.environ.get('QDRANT_PORT', '6333')),
            "collection_name": f"{os.environ.get('USER', 'default_user')}_memories"
        }

    def update_qdrant_config(self, qdrant_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update Qdrant configuration.
        
        Args:
            qdrant_config: New Qdrant configuration
            
        Returns:
            Dict[str, Any]: Update result
        """
        # Validate the new Qdrant configuration
        temp_errors = []
        temp_warnings = []
        self._validate_qdrant_config(qdrant_config, temp_errors, temp_warnings)
        
        if temp_errors:
            raise ValueError(f"Invalid Qdrant configuration: {', '.join(temp_errors)}")
        
        # Update configuration
        with self._config_lock:
            current_config = self._config.copy()
            
            # Update in multiple locations for compatibility
            if 'qdrant' not in current_config:
                current_config['qdrant'] = {}
            current_config['qdrant'].update(qdrant_config)
            
            if 'mem0' in current_config and 'vector_store' in current_config['mem0']:
                vs_config = current_config['mem0']['vector_store']
                if vs_config.get('provider') == 'qdrant':
                    if 'config' not in vs_config:
                        vs_config['config'] = {}
                    vs_config['config'].update(qdrant_config)
            
            if 'vector_store' in current_config:
                vs_config = current_config['vector_store']
                if vs_config.get('provider') == 'qdrant':
                    if 'config' not in vs_config:
                        vs_config['config'] = {}
                    vs_config['config'].update(qdrant_config)
        
        # Use the standard update method to apply changes
        return self.update_config(current_config, save_to_db=True)


# Global instance accessor
def get_config_manager() -> ConfigManager:
    """Get the global ConfigManager instance."""
    return ConfigManager.get_instance()
