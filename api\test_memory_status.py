#!/usr/bin/env python3
"""
Test script to check memory operation queue status and degradation mode.
"""

import sys
import os
import json
from datetime import datetime

# Add the current directory to Python path so we can import app modules
sys.path.insert(0, '/mnt/d/_<PERSON>-Dev/memory-master/api')

def get_memory_status():
    """Get comprehensive memory system status."""
    try:
        from app.utils.memory import MemoryC<PERSON><PERSON><PERSON>leton
        from app.health_service import HealthService
        from app.degradation_status import get_system_health_status, format_status_for_display
        
        print("=== Memory Master Status Check ===")
        print(f"Timestamp: {datetime.now().isoformat()}")
        print()
        
        # Get memory client singleton
        memory_singleton = MemoryClientSingleton()
        
        # 1. Operation Queue Status
        print("1. OPERATION QUEUE STATUS")
        print("-" * 30)
        queue_status = memory_singleton.get_operation_queue_status()
        for key, value in queue_status.items():
            print(f"   {key}: {value}")
        print()
        
        # 2. Degradation Status
        print("2. DEGRADATION STATUS")
        print("-" * 25)
        degradation_status = memory_singleton.get_degradation_status()
        for key, value in degradation_status.items():
            if key == 'last_recovery_attempt' or key == 'last_vector_store_check':
                if value:
                    formatted_time = datetime.fromtimestamp(value).isoformat()
                    print(f"   {key}: {formatted_time}")
                else:
                    print(f"   {key}: None")
            else:
                print(f"   {key}: {value}")
        print()
        
        # 3. Memory Client Health
        print("3. MEMORY CLIENT HEALTH")
        print("-" * 26)
        is_healthy = memory_singleton.is_healthy()
        health_status = memory_singleton.get_health_status()
        print(f"   healthy: {is_healthy}")
        print(f"   client_exists: {health_status.get('client_exists', False)}")
        print(f"   config_hash: {health_status.get('config_hash', 'None')}")
        print(f"   last_health_check: {health_status.get('last_health_check', 'None')}")
        for detail in health_status.get('details', []):
            print(f"   detail: {detail}")
        print()
        
        # 4. Connectivity Status
        print("4. CONNECTIVITY STATUS")
        print("-" * 24)
        connectivity_status = memory_singleton.get_connectivity_status()
        for key, value in connectivity_status.items():
            if key in ['last_check_time', 'last_alert_time', 'next_check_time']:
                if value:
                    formatted_time = datetime.fromtimestamp(value).isoformat()
                    print(f"   {key}: {formatted_time}")
                else:
                    print(f"   {key}: None")
            elif key == 'uptime_percentage':
                if value is not None:
                    print(f"   {key}: {value:.2%}")
                else:
                    print(f"   {key}: None")
            else:
                print(f"   {key}: {value}")
        print()
        
        # 5. Connectivity Metrics
        print("5. CONNECTIVITY METRICS")
        print("-" * 25)
        connectivity_metrics = memory_singleton.get_connectivity_metrics()
        for key, value in connectivity_metrics.items():
            if key in ['monitoring_start_time', 'latest_check_time']:
                if value:
                    formatted_time = datetime.fromtimestamp(value).isoformat()
                    print(f"   {key}: {formatted_time}")
                else:
                    print(f"   {key}: None")
            elif key == 'uptime_percentage':
                if value is not None:
                    print(f"   {key}: {value:.2%}")
                else:
                    print(f"   {key}: None")
            elif key == 'average_check_interval':
                if value is not None:
                    print(f"   {key}: {value:.2f}s")
                else:
                    print(f"   {key}: None")
            else:
                print(f"   {key}: {value}")
        print()
        
        # 6. System Health Status (from health service)
        print("6. SYSTEM HEALTH STATUS")
        print("-" * 26)
        try:
            system_health = get_system_health_status()
            formatted_status = format_status_for_display(system_health)
            print(formatted_status)
            print()
            
            # Also show raw JSON for programmatic access
            print("RAW JSON STATUS:")
            print("-" * 18)
            print(json.dumps(system_health, indent=2, default=str))
            
        except Exception as e:
            print(f"   Error getting system health: {e}")
        
        # 7. Operation Metrics (if enhanced logging available)
        print("\n7. OPERATION METRICS")
        print("-" * 20)
        try:
            from app.enhanced_logging import operation_logger
            metrics = operation_logger.get_operation_metrics()
            active_ops = operation_logger.get_active_operations()
            
            print(f"   Active operations: {len(active_ops)}")
            print(f"   Metrics timestamp: {datetime.fromtimestamp(metrics['metrics_timestamp']).isoformat()}")
            
            for op_type, stats in metrics.get('operations', {}).items():
                success_rate = (stats['success_count'] / stats['total_count'] * 100) if stats['total_count'] > 0 else 0
                print(f"   {op_type}:")
                print(f"      total: {stats['total_count']}, success_rate: {success_rate:.1f}%")
                if stats.get('avg_duration_ms'):
                    print(f"      avg_duration: {stats['avg_duration_ms']:.1f}ms")
                    
        except ImportError:
            print("   Enhanced logging not available")
        except Exception as e:
            print(f"   Error getting operation metrics: {e}")
            
    except Exception as e:
        print(f"Error getting memory status: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    get_memory_status()