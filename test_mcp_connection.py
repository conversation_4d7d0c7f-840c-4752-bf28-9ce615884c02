#!/usr/bin/env python3
"""
Test script to verify MCP server connectivity and functionality
"""
import requests
import json
import time
import sys

def test_health_endpoint():
    """Test the basic health endpoint"""
    try:
        response = requests.get("http://localhost:8765/health", timeout=5)
        if response.status_code == 200:
            print("✅ Health endpoint: OK")
            print(f"   Response: {response.json()}")
            return True
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health endpoint error: {e}")
        return False

def test_mcp_health():
    """Test the MCP health endpoint"""
    try:
        response = requests.get("http://localhost:8765/mcp/health", timeout=5)
        if response.status_code == 200:
            print("✅ MCP Health endpoint: OK")
            data = response.json()
            print(f"   Status: {data.get('status', 'unknown')}")
            if 'diagnostics' in data:
                print(f"   Diagnostics: {data['diagnostics']}")
            return True
        else:
            print(f"❌ MCP Health endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ MCP Health endpoint error: {e}")
        return False

def test_sse_connection():
    """Test SSE connection establishment"""
    try:
        url = "http://localhost:8765/mcp/claude/sse/aungheinaye"
        params = {
            "api_key": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY"
        }
        
        response = requests.get(url, params=params, timeout=5, stream=True)
        if response.status_code == 200:
            print("✅ SSE Connection: OK")
            print(f"   Content-Type: {response.headers.get('content-type')}")
            
            # Read first few lines of SSE stream
            lines_read = 0
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    print(f"   SSE: {line}")
                    lines_read += 1
                    if lines_read >= 3:  # Read first 3 lines
                        break
            return True
        else:
            print(f"❌ SSE Connection failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ SSE Connection error: {e}")
        return False

def main():
    print("🧪 Testing MCP Server Connectivity")
    print("=" * 50)
    
    # Wait a moment for server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    tests = [
        ("Basic Health Check", test_health_endpoint),
        ("MCP Health Check", test_mcp_health),
        ("SSE Connection", test_sse_connection),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    all_passed = True
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status}: {test_name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! MCP server is working correctly.")
        return 0
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
